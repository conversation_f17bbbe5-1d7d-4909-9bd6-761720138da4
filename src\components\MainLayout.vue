<template>
  <div class="main-layout">
    <div class="tabs">
      <button
        :class="['tab-button', { active: activeTab === 'image' }]"
        @click="activeTab = 'image'"
      >
        图片编辑
      </button>
      <button
        :class="['tab-button', { active: activeTab === 'exr' }]"
        @click="activeTab = 'exr'"
      >
        EXR编辑
      </button>
    </div>

    <div class="content">
      <ImageEditor v-show="activeTab === 'image'" />
      <ExrEditor v-show="activeTab === 'exr'" />
    </div>
  </div>
</template>

<script>
import ImageEditor from './ImageEditor.vue';
import ExrEditor from './ExrEditor.vue';

export default {
  name: 'MainLayout',
  components: {
    ImageEditor,
    ExrEditor
  },
  data() {
    return {
      activeTab: 'exr'
    };
  }
};
</script>

<style scoped>
.main-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f7fa;
}

.tabs {
  display: flex;
  background-color: #ffffff;
  padding: 0 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.tab-button {
  padding: 16px 24px;
  font-size: 16px;
  font-weight: 500;
  color: #64748b;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.tab-button:hover {
  color: #3b82f6;
}

.tab-button.active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
}

.content {
  flex: 1;
  overflow: hidden;
  padding: 20px;
}
</style>
# 🔍 文件加载错误诊断指南

## ❌ **当前问题**
出现了 "文件加载失败，请检查文件格式是否正确" 的错误提示。

## 🔧 **已添加的调试功能**

### 1. **详细错误日志**
现在会在控制台输出详细的错误信息：
```javascript
console.error('File processing failed:', error);
console.error('Error details:', {
  message: error.message,
  stack: error.stack,
  name: error.name
});
```

### 2. **分步骤调试**
每个关键步骤都会输出日志：
```javascript
- "Loading file: [filename], size: [size]MB"
- "Loading EXR WASM module..."
- "WASM module loaded successfully"
- "Creating temporary file: [filename]"
- "Getting EXR channels..."
- "Getting EXR dimensions..."
```

### 3. **文件格式验证**
添加了基础的文件格式检查：
```javascript
if (!file.name.toLowerCase().endsWith('.exr')) {
  alert('请选择有效的EXR文件（.exr格式）');
  return;
}
```

## 🧪 **诊断步骤**

### 第一步：检查控制台日志
请打开浏览器开发者工具（F12），查看Console标签页，然后尝试加载EXR文件。

**应该看到的日志序列：**
```
1. "Loading file: [filename], size: [size]MB"
2. "loadExrModule called"
3. "Loading EXR WASM script..." 或 "Using cached EXR module"
4. "EXR WASM script loaded" 或 "WASM module loaded successfully"
5. "Processing EXR on main thread..."
6. "Creating temporary file: [filename]"
7. "Getting EXR channels..."
8. "Getting EXR dimensions..."
```

**如果在某个步骤失败，会看到对应的错误信息。**

### 第二步：常见错误类型

#### 🔴 **WASM加载失败**
**错误信息**: "WASM模块加载失败"
**可能原因**:
- 网络连接问题
- WASM文件路径错误
- 浏览器不支持WASM

**解决方案**:
1. 检查网络连接
2. 确认 `/wasm/exr_bridge.js` 和 `/wasm/exr_bridge.wasm` 文件存在
3. 尝试直接访问: `http://localhost:5173/wasm/exr_bridge.js`

#### 🔴 **文件格式错误**
**错误信息**: "EXR通道读取失败" 或 "EXR尺寸获取失败"
**可能原因**:
- 文件不是有效的EXR格式
- 文件损坏
- EXR文件版本不兼容

**解决方案**:
1. 确认文件是有效的EXR文件
2. 尝试用其他EXR查看器打开文件验证
3. 尝试不同的EXR文件

#### 🔴 **内存不足**
**错误信息**: 包含 "memory" 或 "allocation" 的错误
**可能原因**:
- EXR文件过大
- 浏览器内存限制

**解决方案**:
1. 尝试较小的EXR文件
2. 关闭其他浏览器标签页
3. 重启浏览器

## 📋 **测试清单**

### 基础测试
- [ ] 浏览器开发者工具已打开
- [ ] 选择了有效的.exr文件
- [ ] 文件大小合理（<100MB）
- [ ] 网络连接正常

### WASM测试
- [ ] 访问 `http://localhost:5173/wasm/exr_bridge.js` 返回JavaScript代码
- [ ] 访问 `http://localhost:5173/wasm/exr_bridge.wasm` 下载WASM文件
- [ ] 控制台显示 "EXR WASM script loaded"

### 文件测试
- [ ] 控制台显示文件名和大小
- [ ] 控制台显示 "Temporary file created successfully"
- [ ] 控制台显示 "Channels retrieved successfully"
- [ ] 控制台显示 "EXR dimensions: [width]x[height]"

## 🔧 **手动测试WASM**

如果怀疑WASM有问题，可以在控制台手动测试：

```javascript
// 1. 测试WASM脚本加载
fetch('/wasm/exr_bridge.js')
  .then(response => response.text())
  .then(text => console.log('WASM script size:', text.length))
  .catch(error => console.error('WASM script load failed:', error));

// 2. 测试WASM文件加载
fetch('/wasm/exr_bridge.wasm')
  .then(response => response.arrayBuffer())
  .then(buffer => console.log('WASM file size:', buffer.byteLength))
  .catch(error => console.error('WASM file load failed:', error));
```

## 📊 **错误信息对照表**

| 错误信息 | 可能原因 | 解决方案 |
|----------|----------|----------|
| "WASM模块加载失败" | WASM文件无法加载 | 检查网络和文件路径 |
| "EXR通道读取失败" | 文件格式问题 | 验证EXR文件有效性 |
| "EXR尺寸获取失败" | 文件损坏或格式错误 | 尝试其他EXR文件 |
| "无效的EXR尺寸" | 文件内容异常 | 检查文件完整性 |
| "Worker处理失败" | Worker相关问题 | 已自动回退到主线程 |

## 🎯 **下一步操作**

1. **打开开发者工具**: 按F12打开Console标签页
2. **尝试加载文件**: 选择一个EXR文件
3. **查看日志**: 记录控制台输出的错误信息
4. **报告问题**: 将具体的错误信息反馈给我

## 💡 **常见解决方案**

### 如果是WASM问题
```bash
# 检查WASM文件是否存在
ls public/wasm/
# 应该看到: exr_bridge.js 和 exr_bridge.wasm
```

### 如果是文件格式问题
- 确保文件扩展名是 `.exr`
- 尝试用其他软件（如Photoshop、Blender）打开验证
- 尝试不同来源的EXR文件

### 如果是浏览器问题
- 尝试Chrome或Firefox最新版本
- 清除浏览器缓存
- 禁用浏览器扩展

**🔍 请按照这个指南进行测试，并将控制台的具体错误信息告诉我，这样我就能准确定位问题所在！**

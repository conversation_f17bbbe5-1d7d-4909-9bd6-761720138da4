// EXR处理Worker - 在后台线程处理EXR文件解析
// 避免阻塞主线程UI

let exrModule = null;

// 加载EXR WASM模块
async function loadExrModule() {
  if (exrModule) return exrModule;
  
  // 在Worker中加载WASM模块
  importScripts('/wasm/exr_bridge.js');
  
  return new Promise((resolve, reject) => {
    if (self.ExrModule) {
      self.ExrModule().then(module => {
        exrModule = module;
        resolve(module);
      }).catch(reject);
    } else {
      reject(new Error('ExrModule not available'));
    }
  });
}

// 处理EXR文件信息提取
async function processExrInfo(arrayBuffer) {
  try {
    const Module = await loadExrModule();
    const tempFileName = 'worker_input_' + Date.now() + '.exr';
    
    // 在Worker的文件系统中创建文件
    Module.FS_createDataFile('/', tempFileName, new Uint8Array(arrayBuffer), true, true);
    
    // 获取基本信息
    const width = Module.get_exr_width(tempFileName);
    const height = Module.get_exr_height(tempFileName);
    const channels = Module.get_exr_channels(tempFileName);
    
    // 转换通道数据
    let jsChannels = [];
    if (channels) {
      if (typeof channels.toArray === 'function') {
        jsChannels = channels.toArray();
      } else if (typeof channels.size === 'function' && typeof channels.get === 'function') {
        const n = channels.size();
        for (let i = 0; i < n; i++) {
          jsChannels.push(channels.get(i));
        }
      } else if (typeof channels.length === 'number' && typeof channels.get === 'function') {
        for (let i = 0; i < channels.length; i++) {
          jsChannels.push(channels.get(i));
        }
      }
    }
    
    // 分析灯光组
    const channelMap = {};
    for (const ch of jsChannels) {
      const match = ch.match(/^(.*)\.[RGB]$/i);
      const group = match ? match[1] : ch;
      if (!channelMap[group]) channelMap[group] = [];
      channelMap[group].push(ch);
    }
    
    const lightGroups = [];
    for (const group in channelMap) {
      if (/light|Light|VRayLight|layer|Layer|AOV|aov/.test(group)) {
        const channelsInGroup = channelMap[group];
        const hasR = channelsInGroup.some(ch => ch.endsWith('.R'));
        const hasG = channelsInGroup.some(ch => ch.endsWith('.G'));
        const hasB = channelsInGroup.some(ch => ch.endsWith('.B'));
        
        if (hasR && hasG && hasB) {
          lightGroups.push({
            name: group,
            channels: channelsInGroup
          });
        }
      }
    }
    
    // 清理临时文件
    Module.FS_unlink('/' + tempFileName);
    
    return {
      success: true,
      width,
      height,
      lightGroups,
      allChannels: jsChannels
    };
    
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}

// 处理单个通道数据提取
async function processChannelData(arrayBuffer, channelName) {
  try {
    const Module = await loadExrModule();
    const tempFileName = 'worker_channel_' + Date.now() + '.exr';
    
    Module.FS_createDataFile('/', tempFileName, new Uint8Array(arrayBuffer), true, true);
    
    const channelData = Module.get_exr_channel(tempFileName, channelName);
    
    // 转换为Float32Array
    let floatArray = null;
    if (channelData && typeof channelData.size === 'function' && typeof channelData.get === 'function') {
      const n = channelData.size();
      floatArray = new Float32Array(n);
      for (let i = 0; i < n; i++) {
        floatArray[i] = channelData.get(i);
      }
    } else if (channelData instanceof Float32Array) {
      floatArray = channelData;
    } else if (channelData && typeof channelData.toArray === 'function') {
      floatArray = new Float32Array(channelData.toArray());
    }
    
    Module.FS_unlink('/' + tempFileName);
    
    return {
      success: true,
      channelName,
      data: floatArray
    };
    
  } catch (error) {
    return {
      success: false,
      channelName,
      error: error.message
    };
  }
}

// Worker消息处理
self.onmessage = async function(e) {
  const { type, data, id } = e.data;
  
  try {
    switch (type) {
      case 'PROCESS_EXR_INFO':
        const info = await processExrInfo(data.arrayBuffer);
        self.postMessage({
          type: 'EXR_INFO_RESULT',
          id,
          data: info
        });
        break;
        
      case 'PROCESS_CHANNEL_DATA':
        const channelResult = await processChannelData(data.arrayBuffer, data.channelName);
        self.postMessage({
          type: 'CHANNEL_DATA_RESULT',
          id,
          data: channelResult
        });
        break;
        
      default:
        self.postMessage({
          type: 'ERROR',
          id,
          data: { error: 'Unknown message type: ' + type }
        });
    }
  } catch (error) {
    self.postMessage({
      type: 'ERROR',
      id,
      data: { error: error.message }
    });
  }
};

// Worker初始化完成
self.postMessage({
  type: 'WORKER_READY'
});

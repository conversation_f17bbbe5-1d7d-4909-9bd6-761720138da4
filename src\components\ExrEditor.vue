<template>
  <div class="exr-editor">
    <div class="sidebar">
      <h3>灯光组</h3>
      <div v-for="group in Object.keys(groupToChannels)" :key="group" style="margin-bottom: 16px;">
        <label style="display: block; margin-bottom: 4px; text-align: left;">
          <input type="checkbox" v-model="selectedChannels" :value="group" @change="updatePreview" style="margin-right: 6px; vertical-align: middle;" />
          <span style="vertical-align: middle;">{{ group }}</span>
        </label>
        <div style="display: flex; align-items: center; gap: 8px;">
          <span style="width: 40px;">强度</span>
          <input type="range" min="0" max="10" step="0.1" v-model="groupScales[group]" @input="updatePreview" style="flex:1; width: 100%; vertical-align: middle;" />
          <input type="number" v-model.number="groupScales[group]" @input="updatePreview" @keyup.enter="updatePreview" style="width: 80px; text-align: center;" oninput="this.style.webkitAppearance='none';">
        </div>
        <div style="display: flex; align-items: center; gap: 8px;">
          <span style="width: 40px;">色温</span>
          <input type="range" min="1000" max="15000" step="100" v-model="groupColorTemperatures[group]" @input="updatePreview" style="flex:1; width: 100%; vertical-align: middle;" />
          <input type="number" v-model.number="groupColorTemperatures[group]" @input="updatePreview" @keyup.enter="updatePreview" style="width: 80px; text-align: center;" min="1000" max="15000">
        </div>
        <div style="display: flex; align-items: center; gap: 4px; margin-top: 4px;">
          <span style="width: 40px; font-size: 12px;">预设</span>
          <select v-model="groupColorTemperatures[group]" @change="updatePreview" style="flex: 1; padding: 2px; font-size: 12px;">
            <option v-for="preset in colorTempPresets" :key="preset.name" :value="preset.temp">
              {{ preset.name }} ({{ preset.temp }}K)
            </option>
          </select>
        </div>
      </div>
      <input type="file" accept=".exr" @change="onFileChange" />

      <!-- 缓存状态显示 -->
      <div v-if="!isLoading && Object.keys(lightTextures).length > 0" class="cache-info">
        <h4>缓存状态</h4>
        <p>已缓存: {{ textureCache.getStats().totalEntries }} 个纹理</p>
        <p>内存使用: {{ (textureCache.getStats().currentSize / 1024 / 1024).toFixed(1) }}MB / {{ (textureCache.getStats().maxSize / 1024 / 1024).toFixed(0) }}MB</p>
        <div class="cache-bar">
          <div class="cache-fill" :style="{ width: textureCache.getStats().usagePercent + '%' }"></div>
        </div>
      </div>
    </div>
    <div class="preview">
      <h3>实时预览 (GPU 加速)</h3>
      <div class="canvas-container">
        <canvas ref="canvas"></canvas>

        <!-- 加载进度指示器 - 覆盖在canvas上层 -->
        <div v-if="isLoading" class="loading-overlay">
          <div class="loading-indicator">
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: loadingProgress + '%' }"></div>
            </div>
            <div class="progress-info">
              <span class="progress-percent">{{ Math.round(loadingProgress) }}%</span>
              <span class="progress-time">{{ formatElapsedTime(loadingElapsedTime) }}</span>
            </div>
            <p class="progress-text">正在加载 EXR 文件...</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import vertexShaderSource from '../shaders/vertexShader.vert?raw';
import fragmentShaderSource from '../shaders/lightChannelShader.frag?raw';
import { globalTextureCache } from '../utils/TextureCache.js';
import { getColorTempPresets } from '../utils/ColorTemperature.js';

let exrModule = null;
let exrWorker = null;
function loadExrModule() {
  return new Promise((resolve, reject) => {
    if (exrModule) return resolve(exrModule);
    if (window.ExrModule) {
      window.ExrModule().then(m => {
        exrModule = m;
        resolve(exrModule);
      });
      return;
    }
    const script = document.createElement('script');
    script.src = '/wasm/exr_bridge.js';
    script.onload = () => {
      window.ExrModule().then(m => {
        exrModule = m;
        resolve(exrModule);
      });
    };
    script.onerror = reject;
    document.body.appendChild(script);
  });
}

export default {
  data() {
    return {
      gl: null,
      shaderProgram: null,
      positionBuffer: null,
      texCoordBuffer: null,
      indexBuffer: null,
      lightTextures: {},
      attributeLocations: {},
      uniformLocations: {},
      exrObj: null,
      width: 0,
      height: 0,
      groupToChannels: {},
      selectedChannels: [],
      groupScales: {},
      groupColorTemperatures: {}, // 新增色温数据模型
      // 新增优化相关状态
      isLoading: false,
      loadingProgress: 0,
      loadingStartTime: 0, // 加载开始时间
      loadingElapsedTime: 0, // 已用时间
      loadingTimer: null, // 计时器

      renderDebounceTimer: null,
      textureCache: globalTextureCache, // 使用全局纹理缓存
      lowResPreview: null, // 低分辨率预览
      workerReady: false,
      pendingWorkerTasks: new Map(),
      workerTaskId: 0,
      colorTempPresets: getColorTempPresets(), // 色温预设
    };
  },
  async mounted() {
    // 初始化Worker
    this.initWorker();

    const canvas = this.$refs.canvas;
    this.gl = canvas.getContext('webgl2');
    if (!this.gl) {
      alert('无法初始化 WebGL2，您的浏览器可能不支持。请尝试支持 WebGL2 的浏览器。');
      return;
    }

    // 设置纹理缓存的WebGL上下文
    this.textureCache.setGL(this.gl);
    const vertexShader = this.compileShader(this.gl, this.gl.VERTEX_SHADER, vertexShaderSource);
    const fragmentShader = this.compileShader(this.gl, this.gl.FRAGMENT_SHADER, fragmentShaderSource);
    if (!vertexShader || !fragmentShader) {
      return;
    }
    this.shaderProgram = this.initShaderProgram(this.gl, vertexShader, fragmentShader);
    if (!this.shaderProgram) {
      return;
    }
    this.gl.useProgram(this.shaderProgram);
    this.attributeLocations = {
      position: this.gl.getAttribLocation(this.shaderProgram, 'a_position'),
      texCoord: this.gl.getAttribLocation(this.shaderProgram, 'a_texCoord'),
    };
    this.uniformLocations = {
      lightTextures: this.gl.getUniformLocation(this.shaderProgram, 'u_lightTextures'),
      lightScales: this.gl.getUniformLocation(this.shaderProgram, 'u_lightScales'),
      lightColorTemps: this.gl.getUniformLocation(this.shaderProgram, 'u_lightColorTemps'),
      numLights: this.gl.getUniformLocation(this.shaderProgram, 'u_numLights'),
    };
    const positions = new Float32Array([
      -1.0, 1.0,
      1.0, 1.0,
      -1.0, -1.0,
      1.0, -1.0,
    ]);
    this.positionBuffer = this.gl.createBuffer();
    this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.positionBuffer);
    this.gl.bufferData(this.gl.ARRAY_BUFFER, positions, this.gl.STATIC_DRAW);
    const texCoords = new Float32Array([
      0.0, 1.0,
      1.0, 1.0,
      0.0, 0.0,
      1.0, 0.0,
    ]);
    this.texCoordBuffer = this.gl.createBuffer();
    this.gl.bindBuffer(this.gl.ARRAY_BUFFER, this.texCoordBuffer);
    this.gl.bufferData(this.gl.ARRAY_BUFFER, texCoords, this.gl.STATIC_DRAW);
    const indices = new Uint16Array([0, 1, 2, 1, 3, 2]);
    this.indexBuffer = this.gl.createBuffer();
    this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer);
    this.gl.bufferData(this.gl.ELEMENT_ARRAY_BUFFER, indices, this.gl.STATIC_DRAW);
    this.gl.bindBuffer(this.gl.ARRAY_BUFFER, null);
    this.gl.bindBuffer(this.gl.ELEMENT_ARRAY_BUFFER, null);
  },
  methods: {
    // 初始化Web Worker
    initWorker() {
      try {
        console.log('Initializing EXR Worker...');

        // 启用简化的Worker处理
        exrWorker = new Worker(new URL('../workers/exr-worker-simple.js', import.meta.url));

        exrWorker.onmessage = (e) => {
          const { type, id, data } = e.data;

          switch (type) {
            case 'WORKER_READY':
              this.workerReady = true;
              console.log('EXR Worker ready');
              break;

            case 'EXR_INFO_RESULT':
            case 'CHANNEL_DATA_RESULT':
            case 'ERROR':
              if (this.pendingWorkerTasks.has(id)) {
                const { resolve, reject } = this.pendingWorkerTasks.get(id);
                this.pendingWorkerTasks.delete(id);

                if (type === 'ERROR') {
                  reject(new Error(data.error));
                } else {
                  resolve(data);
                }
              }
              break;
          }
        };

        exrWorker.onerror = (error) => {
          console.error('Worker error:', error);
          this.workerReady = false;
          console.log('Falling back to main thread processing');
        };

      } catch (error) {
        console.warn('Failed to initialize worker, falling back to main thread:', error);
        this.workerReady = false;
      }
    },

    // 发送任务到Worker
    sendWorkerTask(type, data) {
      return new Promise((resolve, reject) => {
        if (!this.workerReady || !exrWorker) {
          reject(new Error('Worker not ready'));
          return;
        }

        const id = ++this.workerTaskId;
        this.pendingWorkerTasks.set(id, { resolve, reject });

        exrWorker.postMessage({ type, data, id });

        // 设置超时
        setTimeout(() => {
          if (this.pendingWorkerTasks.has(id)) {
            this.pendingWorkerTasks.delete(id);
            reject(new Error('Worker task timeout'));
          }
        }, 30000); // 30秒超时
      });
    },

    // 格式化加载时间显示
    formatElapsedTime(seconds) {
      if (seconds < 60) {
        return `${seconds.toFixed(1)}s`;
      } else {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = (seconds % 60).toFixed(1);
        return `${minutes}m ${remainingSeconds}s`;
      }
    },

    // 开始加载计时
    startLoadingTimer() {
      this.isLoading = true;
      this.loadingProgress = 0;
      this.loadingStartTime = Date.now();
      this.loadingElapsedTime = 0;

      // 每100ms更新一次时间显示
      this.loadingTimer = setInterval(() => {
        this.loadingElapsedTime = (Date.now() - this.loadingStartTime) / 1000;
      }, 100);
    },

    // 停止加载计时
    stopLoadingTimer() {
      this.isLoading = false;
      if (this.loadingTimer) {
        clearInterval(this.loadingTimer);
        this.loadingTimer = null;
      }
      console.log(`Loading completed in ${this.formatElapsedTime(this.loadingElapsedTime)}`);
    },

    compileShader(gl, type, source) {
      const shader = gl.createShader(type);
      gl.shaderSource(shader, source);
      gl.compileShader(shader);
      if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
        console.error('编译着色器时出错: ' + gl.getShaderInfoLog(shader));
        gl.deleteShader(shader);
        return null;
      }
      return shader;
    },
    initShaderProgram(gl, vs, fs) {
      const shaderProgram = gl.createProgram();
      gl.attachShader(shaderProgram, vs);
      gl.attachShader(shaderProgram, fs);
      gl.linkProgram(shaderProgram);
      if (!gl.getProgramParameter(shaderProgram, gl.LINK_STATUS)) {
        console.error('无法初始化着色器程序: ' + gl.getProgramInfoLog(shaderProgram));
        return null;
      }
      return shaderProgram;
    },
    async onFileChange(e) {
      const file = e.target.files[0];
      if (!file) return;

      // 开始加载计时
      this.startLoadingTimer();

      // 清理现有资源
      this.textureCache.clear();
      this.lightTextures = {};
      this.groupScales = {};
      this.groupColorTemperatures = {};
      this.selectedChannels = [];
      this.width = 0;
      this.height = 0;
      this.groupToChannels = {};

      try {
        const arrayBuffer = await file.arrayBuffer();

        // 如果Worker可用，先进行文件验证
        if (this.workerReady) {
          try {
            console.log('Using Worker for file validation...');
            const validationResult = await this.sendWorkerTask('PROCESS_EXR_INFO', { arrayBuffer });
            if (validationResult.success) {
              console.log('File validation passed, processing on main thread...');
            }
          } catch (workerError) {
            console.warn('Worker validation failed, continuing with main thread:', workerError);
          }
        }

        // 主要处理逻辑在主线程
        await this.processFileMainThread(arrayBuffer);

      } catch (error) {
        console.error('File processing failed:', error);
        alert('文件加载失败，请检查文件格式是否正确。');
      } finally {
        // 停止加载计时
        this.stopLoadingTimer();
      }
    },



    // 主线程处理文件（回退方案）
    async processFileMainThread(arrayBuffer) {
      console.log('Processing EXR on main thread...');

      const Module = await loadExrModule();
      const tempFileName = 'input_' + Date.now() + '.exr';
      Module.FS_createDataFile('/', tempFileName, new Uint8Array(arrayBuffer), true, true);

      const channels = Module.get_exr_channels(tempFileName);
      let jsChannels = [];
      if (channels) {
        if (typeof channels.toArray === 'function') {
          jsChannels = channels.toArray();
        } else if (typeof channels.size === 'function' && typeof channels.get === 'function') {
          const n = channels.size();
          for (let i = 0; i < n; i++) {
            jsChannels.push(channels.get(i));
          }
        } else if (typeof channels.length === 'number' && typeof channels.get === 'function') {
          for (let i = 0; i < channels.length; i++) {
            jsChannels.push(channels.get(i));
          }
        } else if (typeof channels === 'object') {
          for (let key in channels) {
            if (channels.hasOwnProperty(key) && !isNaN(Number(key))) {
              jsChannels.push(channels[key]);
            }
          }
        }
      }

      this.width = Module.get_exr_width(tempFileName);
      this.height = Module.get_exr_height(tempFileName);

      const canvas = this.$refs.canvas;
      canvas.width = this.width;
      canvas.height = this.height;
      if (this.gl) {
        this.gl.viewport(0, 0, this.width, this.height);
      }

      const channelMap = {};
      for (const ch of jsChannels) {
        console.log('Channel:', ch);
        const match = ch.match(/^(.*)\.[RGB]$/i);
        const group = match ? match[1] : ch;
        if (!channelMap[group]) channelMap[group] = [];
        channelMap[group].push(ch);
      }

      const lightGroupsToLoad = [];
      for (const group in channelMap) {
        if (/light|Light|VRayLight|layer|Layer|AOV|aov/.test(group)) {
          const channelsInGroup = channelMap[group];
          const hasR = channelsInGroup.some(ch => ch.endsWith('.R'));
          const hasG = channelsInGroup.some(ch => ch.endsWith('.G'));
          const hasB = channelsInGroup.some(ch => ch.endsWith('.B'));
          if (hasR && hasG && hasB) {
            this.groupToChannels[group] = channelsInGroup;
            lightGroupsToLoad.push(group);
            if (this.groupScales[group] === undefined) {
              this.groupScales[group] = 1;
            }
            if (this.groupColorTemperatures[group] === undefined) {
              this.groupColorTemperatures[group] = 6500;
            }
          } else {
            console.warn(`Group "${group}" identified as light group but does not have all R, G, B channels (${hasR?'R':'_'}, ${hasG?'G':'_'}, ${hasB?'B':'_'}). Skipping.`);
          }
        }
      }

      this.selectedChannels = lightGroupsToLoad;

      // 使用渐进式加载替代并行加载
      await this.loadTexturesProgressively(lightGroupsToLoad, Module, tempFileName);

      Module.FS_unlink('/' + tempFileName);
      this.$nextTick(this.updatePreview);
    },
    async loadAndCombineLightTexture(Module, fileName, groupName, rChannelName, gChannelName, bChannelName) {
      if (!this.gl) {
        console.error("WebGL context is not available to create texture.");
        return;
      }
      const numPixels = this.width * this.height;
      const textureData = new Float32Array(numPixels * 3);
      const rArr = Module.get_exr_channel(fileName, rChannelName);
      const gArr = Module.get_exr_channel(fileName, gChannelName);
      const bArr = Module.get_exr_channel(fileName, bChannelName);
      const convertToFloat32Array = (arr, channelName) => {
        if (arr && typeof arr.size === 'function' && typeof arr.get === 'function') {
          const n = arr.size();
          const floatArr = new Float32Array(n);
          for (let i = 0; i < n; i++) floatArr[i] = arr.get(i);
          return floatArr;
        } else if (arr && typeof arr.length === 'number' && typeof arr.get === 'function') {
          const floatArr = new Float32Array(arr.length);
          for (let i = 0; i < arr.length; i++) floatArr[i] = arr.get(i);
          return floatArr;
        } else if (arr instanceof Float32Array) {
          return arr;
        } else if (arr && typeof arr.toArray === 'function') {
          return new Float32Array(arr.toArray());
        } else if (Array.isArray(arr)) {
          return new Float32Array(arr);
        } else if (arr && typeof arr === 'object' && arr.buffer && arr.byteLength) {
          return new Float32Array(arr);
        }
        console.error(`无法获取或转换通道数据: ${channelName}`, arr);
        return new Float32Array(numPixels);
      };
      const jsRArr = convertToFloat32Array(rArr, rChannelName);
      const jsGArr = convertToFloat32Array(gArr, gChannelName);
      const jsBArr = convertToFloat32Array(bArr, bChannelName);
      if (jsRArr.length !== numPixels || jsGArr.length !== numPixels || jsBArr.length !== numPixels) {
        console.error(`通道数据长度不匹配，无法合并纹理: ${groupName}`);
        return;
      }
      let hasNonZero = false;
      for(let i = 0; i < numPixels; ++i) {
        textureData[i * 3] = jsRArr[i] || 0;
        textureData[i * 3 + 1] = jsGArr[i] || 0;
        textureData[i * 3 + 2] = jsBArr[i] || 0;
        if (textureData[i * 3] !== 0 || textureData[i * 3 + 1] !== 0 || textureData[i * 3 + 2] !== 0) {
          hasNonZero = true;
        }
      }
      const texture = this.gl.createTexture();
      this.gl.bindTexture(this.gl.TEXTURE_2D, texture);
      this.gl.pixelStorei(this.gl.UNPACK_FLIP_Y_WEBGL, true);
      this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_S, this.gl.CLAMP_TO_EDGE);
      this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_WRAP_T, this.gl.CLAMP_TO_EDGE);
      this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MIN_FILTER, this.gl.NEAREST);
      this.gl.texParameteri(this.gl.TEXTURE_2D, this.gl.TEXTURE_MAG_FILTER, this.gl.NEAREST);
      const internalFormat = this.gl.RGB32F;
      const format = this.gl.RGB;
      const type = this.gl.FLOAT;
      this.gl.texImage2D(this.gl.TEXTURE_2D, 0, internalFormat, this.width, this.height, 0, format, type, textureData);
      this.gl.pixelStorei(this.gl.UNPACK_FLIP_Y_WEBGL, false);
      this.gl.bindTexture(this.gl.TEXTURE_2D, null);
      this.lightTextures[groupName] = texture;
    },
    // 优化的防抖渲染方法
    updatePreview() {
      // 清除之前的防抖定时器
      if (this.renderDebounceTimer) {
        clearTimeout(this.renderDebounceTimer);
      }

      // 设置新的防抖定时器，16ms约等于60fps
      this.renderDebounceTimer = setTimeout(() => {
        this.performRender();
      }, 16);
    },

    performRender() {
      const gl = this.gl;
      if (!gl) {
        console.warn("WebGL context is not available in updatePreview.");
        return;
      }
      if (!this.shaderProgram || Object.keys(this.lightTextures).length === 0) {
        console.warn("Shader program not loaded, or no light textures loaded. Skipping render.");
        gl.clearColor(0.0, 0.0, 0.0, 1.0);
        gl.clear(gl.COLOR_BUFFER_BIT);
        return;
      }
      gl.disable(gl.DEPTH_TEST);
      gl.disable(gl.CULL_FACE);
      gl.disable(gl.BLEND);
      gl.useProgram(this.shaderProgram);
      gl.bindBuffer(gl.ARRAY_BUFFER, this.positionBuffer);
      gl.vertexAttribPointer(this.attributeLocations.position, 2, gl.FLOAT, false, 0, 0);
      gl.enableVertexAttribArray(this.attributeLocations.position);
      gl.bindBuffer(gl.ARRAY_BUFFER, this.texCoordBuffer);
      gl.vertexAttribPointer(this.attributeLocations.texCoord, 2, gl.FLOAT, false, 0, 0);
      gl.enableVertexAttribArray(this.attributeLocations.texCoord);
      gl.bindBuffer(gl.ELEMENT_ARRAY_BUFFER, this.indexBuffer);
      gl.clearColor(0.0, 0.0, 0.0, 1.0);
      gl.clear(gl.COLOR_BUFFER_BIT);
      let textureUnitIndex = 0;
      const activeLightTextures = [];
      const activeLightScales = [];
      const activeLightColorTemps = [];
      const MAX_SHADER_LIGHTS = 16;
      for (const group of this.selectedChannels) {
        const lightTexture = this.lightTextures[group];
        if (lightTexture && activeLightTextures.length < MAX_SHADER_LIGHTS) {
          gl.activeTexture(gl.TEXTURE0 + textureUnitIndex);
          gl.bindTexture(gl.TEXTURE_2D, lightTexture);
          activeLightTextures.push(textureUnitIndex);
          activeLightScales.push((this.groupScales[group] ?? 1));
          activeLightColorTemps.push((this.groupColorTemperatures[group] ?? 6500));
          textureUnitIndex++;
        } else if (lightTexture && activeLightTextures.length >= MAX_SHADER_LIGHTS) {
          console.warn(`超过片段着色器支持的最大灯光通道数量 (${MAX_SHADER_LIGHTS})，忽略组: ${group}`);
        } else {
          console.warn(`未找到灯光组 "${group}" 的合并纹理数据。`);
        }
      }
      if (this.uniformLocations.lightTextures) {
        gl.uniform1iv(this.uniformLocations.lightTextures, new Int32Array(activeLightTextures));
      } else {
        console.warn("lightTextures uniform location not found.");
      }
      if (this.uniformLocations.lightScales) {
        gl.uniform1fv(this.uniformLocations.lightScales, new Float32Array(activeLightScales));
      } else {
        console.warn("lightScales uniform location not found.");
      }
      if (this.uniformLocations.lightColorTemps) {
        gl.uniform1fv(this.uniformLocations.lightColorTemps, new Float32Array(activeLightColorTemps));
      } else {
        console.warn("lightColorTemps uniform location not found.");
      }
      if (this.uniformLocations.numLights) {
        gl.uniform1i(this.uniformLocations.numLights, activeLightTextures.length);
      } else {
        console.warn("numLights uniform location not found.");
      }
      gl.drawElements(gl.TRIANGLES, 6, gl.UNSIGNED_SHORT, 0);
    },



    // 新增：渐进式加载方法（主线程回退）
    async loadTexturesProgressively(lightGroupsToLoad, Module, tempFileName) {
      const totalGroups = lightGroupsToLoad.length;

      for (let i = 0; i < totalGroups; i++) {
        const group = lightGroupsToLoad[i];
        const channelsInGroup = this.groupToChannels[group];
        const rChannelName = channelsInGroup.find(ch => ch.endsWith('.R'));
        const gChannelName = channelsInGroup.find(ch => ch.endsWith('.G'));
        const bChannelName = channelsInGroup.find(ch => ch.endsWith('.B'));

        if (rChannelName && gChannelName && bChannelName) {
          await this.loadAndCombineLightTexture(Module, tempFileName, group, rChannelName, gChannelName, bChannelName);

          // 更新进度
          this.loadingProgress = ((i + 1) / totalGroups) * 100;

          // 每加载一个纹理就更新一次预览，提供即时反馈
          this.$nextTick(() => {
            this.updatePreview();
          });

          // 让出控制权给浏览器，避免阻塞UI
          await new Promise(resolve => setTimeout(resolve, 0));
        }
      }
    },

    // 新增：创建低分辨率预览
    async createLowResPreview(Module, tempFileName) {
      // 创建1/4分辨率的预览图
      const previewWidth = Math.max(1, Math.floor(this.width / 4));
      const previewHeight = Math.max(1, Math.floor(this.height / 4));

      // 这里可以实现快速的低分辨率预览逻辑
      // 暂时返回null，实际实现需要根据WASM模块的API
      return null;
    },


  },

  // 组件销毁时清理资源
  beforeUnmount() {
    // 清理防抖定时器
    if (this.renderDebounceTimer) {
      clearTimeout(this.renderDebounceTimer);
    }

    // 清理加载计时器
    if (this.loadingTimer) {
      clearInterval(this.loadingTimer);
    }

    // 清理Worker
    if (exrWorker) {
      exrWorker.terminate();
      exrWorker = null;
    }

    // 清理待处理的Worker任务
    for (const [id, { reject }] of this.pendingWorkerTasks) {
      reject(new Error('Component unmounted'));
    }
    this.pendingWorkerTasks.clear();

    // 清理WebGL资源
    if (this.gl) {
      // 删除所有纹理（通过缓存管理）
      this.textureCache.clear();

      // 删除缓冲区
      if (this.positionBuffer) this.gl.deleteBuffer(this.positionBuffer);
      if (this.texCoordBuffer) this.gl.deleteBuffer(this.texCoordBuffer);
      if (this.indexBuffer) this.gl.deleteBuffer(this.indexBuffer);

      // 删除着色器程序
      if (this.shaderProgram) this.gl.deleteProgram(this.shaderProgram);
    }

    console.log('ExrEditor resources cleaned up');
  }
};
</script>

<style scoped>
.exr-editor {
  display: flex;
  flex-direction: row;
  gap: 20px;
  height: calc(100% - 20px);
  margin-bottom: 20px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.sidebar {
  width: 360px;
  background-color: #f8fafc;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  padding: 16px;
  overflow-y: auto;
}

.sidebar h3 {
  margin: 0 0 16px 0;
  color: #1e293b;
  font-size: 18px;
  font-weight: 600;
}

.preview {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  padding: 16px;
}

.preview h3 {
  margin: 0 0 16px 0;
  color: #1e293b;
  font-size: 18px;
  font-weight: 600;
}

/* Canvas容器样式 */
.canvas-container {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

canvas {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 加载覆盖层样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  height: 6px;
  background: #e2e8f0;
  border-radius: 3px;
  outline: none;
  transition: all 0.2s ease;
}

input[type="range"]:hover {
  background: #cbd5e1;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 16px;
  height: 16px;
  background: #3b82f6;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
}

input[type="range"]::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  background: #2563eb;
}

input[type="range"]::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #3b82f6;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
}

input[type="range"]::-moz-range-thumb:hover {
  transform: scale(1.1);
  background: #2563eb;
}

input[type="number"] {
  width: 60px;
  padding: 4px 8px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 14px;
  color: #475569;
  text-align: center;
  background-color: #ffffff;
}

input[type="number"]:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

input[type="file"] {
  margin-top: 16px;
  padding: 8px;
  border: 1px dashed #e2e8f0;
  border-radius: 4px;
  background-color: #ffffff;
  color: #475569;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

input[type="file"]:hover {
  border-color: #3b82f6;
  background-color: #f8fafc;
}

label {
  color: #475569;
  font-size: 14px;
  font-weight: 500;
}

span {
  color: #64748b;
  font-size: 14px;
}

/* 加载进度指示器样式 */
.loading-indicator {
  padding: 24px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  border: 1px solid rgba(226, 232, 240, 0.8);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(8px);
  min-width: 320px;
  max-width: 400px;
}

.progress-bar {
  width: 100%;
  height: 10px;
  background-color: #e2e8f0;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 12px;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #1d4ed8);
  border-radius: 5px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-percent {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.progress-time {
  font-size: 14px;
  color: #64748b;
  font-family: 'Courier New', monospace;
}

.progress-text {
  margin: 0;
  font-size: 14px;
  color: #475569;
  text-align: center;
  font-weight: 500;
}

/* 缓存状态样式 */
.cache-info {
  margin-top: 16px;
  padding: 12px;
  background-color: #f8fafc;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.cache-info h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #374151;
}

.cache-info p {
  margin: 4px 0;
  font-size: 12px;
  color: #6b7280;
}

.cache-bar {
  width: 100%;
  height: 4px;
  background-color: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
  margin-top: 4px;
}

.cache-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #059669);
  border-radius: 2px;
  transition: width 0.3s ease;
}

/* 选择框样式 */
select {
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  background-color: #ffffff;
  color: #475569;
  font-size: 12px;
}

select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}


</style>

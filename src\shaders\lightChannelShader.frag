#version 300 es
// lightChannelShader.frag
// A fragment shader to composite multiple light channels with adjustable scales.
// Using GLSL ES 3.0 syntax for WebGL2.
// No base layer. Compositing starts with a black background.

precision highp float; // 使用 highp 精度

in vec2 v_texCoord;

#define MAX_LIGHTS 16

uniform sampler2D u_lightTextures[MAX_LIGHTS];
uniform float u_lightScales[MAX_LIGHTS];
uniform float u_lightColorTemps[MAX_LIGHTS];
uniform int u_numLights;

out vec4 fragColor;

// 色温转换函数：将色温(K)转换为RGB系数
vec3 colorTempToRGB(float temp) {
  temp = clamp(temp, 1000.0, 15000.0);

  float r, g, b;

  // 红色通道
  if (temp < 6600.0) {
    r = 1.0;
  } else {
    r = 1.292936 * pow(temp / 100.0 - 60.0, -0.1332047);
    r = clamp(r, 0.0, 1.0);
  }

  // 绿色通道
  if (temp < 6600.0) {
    g = 0.39008157 * log(temp / 100.0) - 0.63184144;
  } else {
    g = 1.129890 * pow(temp / 100.0 - 60.0, -0.0755148);
  }
  g = clamp(g, 0.0, 1.0);

  // 蓝色通道
  if (temp >= 6600.0) {
    b = 1.0;
  } else if (temp < 2000.0) {
    b = 0.0;
  } else {
    b = 0.********* * log(temp / 100.0 - 10.0) - 1.19625408;
    b = clamp(b, 0.0, 1.0);
  }

  return vec3(r, g, b);
}

void main() {
  vec3 finalColor = vec3(0.0); // 初始化为黑色，准备叠加灯光贡献

  // 原始的灯光叠加计算循环
  for (int i = 0; i < MAX_LIGHTS; ++i) {
    // 检查当前循环索引是否小于实际激活的灯光数量
    if (i < u_numLights) {
        vec3 lightColor = vec3(0.0); // 初始化当前灯光颜色

        // 使用 switch 语句根据循环索引 i 采样对应的纹理
        // 编译器可以在编译时确定每个 case 访问的是哪个 sampler
        switch(i) {
            case 0: lightColor = texture(u_lightTextures[0], v_texCoord).rgb; break;
            case 1: lightColor = texture(u_lightTextures[1], v_texCoord).rgb; break;
            case 2: lightColor = texture(u_lightTextures[2], v_texCoord).rgb; break;
            case 3: lightColor = texture(u_lightTextures[3], v_texCoord).rgb; break;
            case 4: lightColor = texture(u_lightTextures[4], v_texCoord).rgb; break;
            case 5: lightColor = texture(u_lightTextures[5], v_texCoord).rgb; break;
            case 6: lightColor = texture(u_lightTextures[6], v_texCoord).rgb; break;
            case 7: lightColor = texture(u_lightTextures[7], v_texCoord).rgb; break;
            case 8: lightColor = texture(u_lightTextures[8], v_texCoord).rgb; break;
            case 9: lightColor = texture(u_lightTextures[9], v_texCoord).rgb; break;
            case 10: lightColor = texture(u_lightTextures[10], v_texCoord).rgb; break;
            case 11: lightColor = texture(u_lightTextures[11], v_texCoord).rgb; break;
            case 12: lightColor = texture(u_lightTextures[12], v_texCoord).rgb; break;
            case 13: lightColor = texture(u_lightTextures[13], v_texCoord).rgb; break;
            case 14: lightColor = texture(u_lightTextures[14], v_texCoord).rgb; break;
            case 15: lightColor = texture(u_lightTextures[15], v_texCoord).rgb; break;
        }

        // 应用色温调整
        vec3 colorTempMultiplier = colorTempToRGB(u_lightColorTemps[i]);

        // 将当前灯光的贡献乘以缩放系数和色温系数后加到最终颜色中
        finalColor += lightColor * u_lightScales[i] * colorTempMultiplier;
    } else {
      // 如果已经处理完所有激活的灯光，可以提前退出循环
      break;
    }
  }

  // 输出最终叠加后的颜色
  fragColor = vec4(finalColor, 1.0);
}
